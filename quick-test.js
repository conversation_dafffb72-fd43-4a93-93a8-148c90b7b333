// 快速测试脚本 - 验证正则表达式和解析逻辑

/**
 * 解析提交消息格式（复制自extension.js）
 */
function parseCommitMessage(message) {
    // 匹配格式：数字 + 空格 + 模块名 + 冒号 + 描述
    // 更灵活的正则表达式，支持更多空格和格式变化
    const regex = /^(\d+)\s+([^:]+?)\s*[:：]\s*(.+)$/;
    const match = message.trim().match(regex);
    
    if (match) {
        return {
            taskNumber: match[1].trim(),
            module: match[2].trim(),
            description: match[3].trim()
        };
    }
    
    // 尝试更宽松的格式匹配
    const looseRegex = /^(\d+)[\s\-_]+([^:：]+?)[\s]*[:：][\s]*(.+)$/;
    const looseMatch = message.trim().match(looseRegex);
    
    if (looseMatch) {
        return {
            taskNumber: looseMatch[1].trim(),
            module: looseMatch[2].trim(),
            description: looseMatch[3].trim()
        };
    }
    
    return null;
}

/**
 * 格式化提交消息
 */
function formatCommitMessage(type, parsed) {
    return `${type}(${parsed.module}) : ${parsed.taskNumber} ${parsed.description}`;
}

// 测试用例
const testCases = [
    '13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示',
    '12345 用户管理模块 : 添加用户权限验证功能',
    '67890 订单处理系统 : 修复订单状态更新bug',
    '11111 报表生成器 : 优化查询性能',
    '22222 API接口层 : 更新接口文档',
    '33333 数据库层 ： 修复连接池问题',  // 中文冒号
    '44444 前端界面-用户体验 : 改进响应式布局',
    // 错误格式测试
    '这是一个普通的提交消息',
    '修复了一个bug',
    '13386 : 缺少模块名称',
    '主数据模型-主数据维护 : 缺少任务编号',
];

console.log('=== Git Commit Formatter 测试 ===\n');

testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase}`);
    
    const parsed = parseCommitMessage(testCase);
    if (parsed) {
        console.log('✅ 解析成功:');
        console.log(`   任务编号: ${parsed.taskNumber}`);
        console.log(`   模块名称: ${parsed.module}`);
        console.log(`   描述: ${parsed.description}`);
        
        const formatted = formatCommitMessage('fix', parsed);
        console.log(`   格式化结果: ${formatted}`);
    } else {
        console.log('❌ 解析失败');
    }
    console.log('');
});

console.log('=== 测试完成 ===');

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        parseCommitMessage,
        formatCommitMessage
    };
}
