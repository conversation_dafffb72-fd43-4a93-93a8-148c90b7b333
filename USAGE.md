# Git Commit Formatter 使用指南

## 安装和开发

### 开发环境设置

1. 确保已安装 Node.js 和 npm
2. 在项目根目录运行：
   ```bash
   npm install
   ```

### 调试插件

1. 在 VSCode 中打开项目文件夹
2. 按 `F5` 或使用 "Run Extension" 调试配置
3. 这将打开一个新的 VSCode 窗口，插件已加载

### 打包插件

1. 安装 vsce（VSCode Extension Manager）：
   ```bash
   npm install -g vsce
   ```

2. 打包插件：
   ```bash
   vsce package
   ```

## 使用方法

### 手动格式化

1. 在编辑器中输入或选中符合格式的提交消息
2. 使用以下任一方式触发格式化：
   - 按快捷键 `Ctrl+Shift+G` (Windows/Linux) 或 `Cmd+Shift+G` (Mac)
   - 右键菜单选择 "格式化Git提交消息"
   - 命令面板 (`Ctrl+Shift+P`) 输入 "格式化Git提交消息"

### 自动检测

插件会自动检测 Git 提交消息文件中符合格式的内容，并提示是否格式化。

### 消息格式

**输入格式：**
```
13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
```

**输出格式：**
```
fix(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示
```

### 支持的提交类型

- **feat**: 新功能
- **fix**: 修复bug  
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建工具或辅助工具的变动

## 配置选项

可以在 VSCode 设置中配置以下选项：

- `gitCommitFormatter.autoDetect`: 是否自动检测并提示格式化（默认：true）
- `gitCommitFormatter.defaultType`: 默认的提交类型（默认：feat）

## 故障排除

### 插件未激活
- 确保在有文本编辑器焦点时使用命令
- 检查插件是否正确安装

### 格式不匹配
- 确保消息格式为：`数字 模块名 : 描述`
- 检查冒号前后是否有正确的空格

### 自动检测不工作
- 确保在 Git 提交消息文件中（文件名包含 COMMIT_EDITMSG 或 git-commit）
- 检查自动检测设置是否启用
