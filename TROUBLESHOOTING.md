# 故障排除指南

## 🔧 常见问题及解决方案

### 1. 快捷键不工作

**问题**: 按 `Ctrl+Shift+G` 没有反应

**解决方案**:
1. 确保当前有文本编辑器处于焦点状态
2. 检查是否有其他插件占用了相同快捷键
3. 尝试通过命令面板使用：
   - 按 `Ctrl+Shift+P`
   - 输入 "格式化Git提交消息"
   - 选择命令执行

### 2. 消息格式不匹配

**问题**: 提示"消息格式不匹配"

**解决方案**:
1. 检查消息格式是否正确：
   ```
   正确格式: 13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
   ```
2. 确保包含以下元素：
   - 任务编号（数字）
   - 空格
   - 模块名称
   - 冒号（支持中文冒号：）
   - 空格
   - 描述

3. 使用测试命令验证：
   - 命令面板 → "测试消息解析"

### 3. 自动检测不工作

**问题**: 在Git提交时没有自动提示

**解决方案**:
1. 检查自动检测是否启用：
   - 命令面板 → "切换自动检测"
2. 确保在Git相关文件中工作
3. 当前版本主要支持Git提交消息文件，VSCode内置Git界面的支持有限

### 4. 插件未激活

**问题**: 找不到插件命令

**解决方案**:
1. 检查插件是否正确安装
2. 重启VSCode
3. 查看输出面板是否有错误信息：
   - 查看 → 输出 → 选择 "Git Commit Formatter"

## 🧪 测试步骤

### 基本功能测试

1. **打开测试文件**:
   ```bash
   code test-commit-message.txt
   ```

2. **选择测试消息**:
   选中任一正确格式的消息行

3. **执行格式化**:
   - 方法1: 按 `Ctrl+Shift+G`
   - 方法2: 右键菜单 → "格式化Git提交消息"
   - 方法3: 命令面板 → "格式化Git提交消息"

4. **选择提交类型**:
   在弹出的菜单中选择合适的类型（如 fix、feat 等）

5. **验证结果**:
   检查消息是否正确格式化

### 解析功能测试

1. **运行测试命令**:
   - 命令面板 → "测试消息解析"
   - 应该显示成功消息

2. **手动测试不同格式**:
   ```
   13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
   12345 用户管理模块 ： 添加用户权限验证功能
   67890 订单处理系统-核心逻辑 : 修复订单状态更新bug
   ```

## 🐛 调试信息

### 查看控制台日志

1. 打开开发者工具：
   - 帮助 → 切换开发人员工具

2. 查看控制台输出：
   - 插件会输出处理的文本和解析结果

### 检查配置

1. 打开设置：
   - 文件 → 首选项 → 设置

2. 搜索 "Git Commit Formatter"

3. 检查配置项：
   - `gitCommitFormatter.autoDetect`: 自动检测开关
   - `gitCommitFormatter.defaultType`: 默认提交类型

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **检查VSCode版本**: 确保使用VSCode 1.60.0或更高版本
2. **重新安装插件**: 卸载后重新安装
3. **查看错误日志**: 帮助 → 切换开发人员工具 → 控制台
4. **创建最小复现案例**: 使用提供的测试文件进行测试

## 🔄 重新安装步骤

1. 卸载当前插件
2. 重启VSCode
3. 运行安装脚本：
   ```bash
   # Windows
   install.bat
   
   # Linux/Mac
   ./install.sh
   ```
4. 重新安装生成的.vsix文件
