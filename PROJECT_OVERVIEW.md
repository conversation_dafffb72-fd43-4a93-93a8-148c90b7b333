# Git Commit Formatter - 项目概览

## 📁 项目结构

```
Git Commit Formatter 完整插件包/
├── .vscode/
│   └── launch.json          # VSCode 调试配置
├── .gitignore              # Git 忽略文件配置
├── CHANGELOG.md            # 版本更新日志
├── extension.js            # 🔥 主要插件逻辑
├── install.bat             # Windows 安装脚本
├── install.sh              # Linux/Mac 安装脚本
├── LICENSE                 # MIT 许可证
├── package.json            # 🔥 插件配置文件
├── PROJECT_OVERVIEW.md     # 项目概览（本文件）
├── README.md               # 项目说明
├── test-commit-message.txt # 测试用例
└── USAGE.md                # 详细使用指南
```

## 🚀 核心功能

### 1. 消息格式转换
- **输入格式**: `13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示`
- **输出格式**: `fix(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示`

### 2. 提交类型支持
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建工具或辅助工具的变动

### 3. 交互方式
- **快捷键**: `Ctrl+Shift+G` (Windows/Linux) / `Cmd+Shift+G` (Mac)
- **右键菜单**: 格式化Git提交消息
- **命令面板**: 搜索 "格式化Git提交消息"
- **自动检测**: 在Git提交文件中自动提示

## 🔧 技术实现

### 核心文件说明

#### `package.json`
- 定义插件元数据和配置
- 注册命令和快捷键
- 配置激活事件和菜单项

#### `extension.js`
- 主要业务逻辑实现
- 正则表达式解析消息格式
- VSCode API 集成
- 用户交互处理

### 关键技术点

1. **正则表达式解析**: `^(\d+)\s+([^:]+?)\s*:\s*(.+)$`
2. **VSCode 命令注册**: `vscode.commands.registerCommand`
3. **文本编辑器操作**: `editor.edit()` 和 `editBuilder.replace()`
4. **快速选择菜单**: `vscode.window.showQuickPick`
5. **配置管理**: `vscode.workspace.getConfiguration`

## 📦 安装和使用

### 开发环境
1. 运行 `install.sh` (Linux/Mac) 或 `install.bat` (Windows)
2. 或手动执行：
   ```bash
   npm install
   npm install -g vsce
   vsce package
   ```

### 插件安装
1. 在 VSCode 中按 `Ctrl+Shift+P`
2. 输入 "Extensions: Install from VSIX..."
3. 选择生成的 `.vsix` 文件

### 开发调试
1. 在 VSCode 中打开项目
2. 按 `F5` 启动调试
3. 在新窗口中测试插件功能

## 🧪 测试

使用 `test-commit-message.txt` 文件中的示例进行测试：

### 正确格式示例
```
13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
12345 用户管理模块 : 添加用户权限验证功能
67890 订单处理系统 : 修复订单状态更新bug
```

### 测试步骤
1. 选中任一正确格式的消息
2. 按 `Ctrl+Shift+G`
3. 选择提交类型
4. 查看格式化结果

## 🔮 扩展可能

- 支持更多提交消息格式
- 添加自定义提交类型
- 集成 Git 工作流
- 支持批量格式化
- 添加消息模板功能

## 📄 许可证

MIT License - 详见 `LICENSE` 文件
