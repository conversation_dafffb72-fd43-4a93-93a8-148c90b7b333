const vscode = require('vscode');

/**
 * 提交类型配置
 */
const COMMIT_TYPES = [
    { label: 'feat', description: '新功能' },
    { label: 'fix', description: '修复bug' },
    { label: 'docs', description: '文档更新' },
    { label: 'style', description: '代码格式调整' },
    { label: 'refactor', description: '代码重构' },
    { label: 'perf', description: '性能优化' },
    { label: 'test', description: '测试相关' },
    { label: 'chore', description: '构建工具或辅助工具的变动' }
];

/**
 * 解析提交消息格式
 * 输入格式：13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
 * @param {string} message 
 * @returns {object|null} 解析结果
 */
function parseCommitMessage(message) {
    // 匹配格式：数字 + 空格 + 模块名 + 冒号 + 描述
    const regex = /^(\d+)\s+([^:]+?)\s*:\s*(.+)$/;
    const match = message.trim().match(regex);
    
    if (match) {
        return {
            taskNumber: match[1].trim(),
            module: match[2].trim(),
            description: match[3].trim()
        };
    }
    
    return null;
}

/**
 * 格式化提交消息
 * @param {string} type 提交类型
 * @param {object} parsed 解析后的消息对象
 * @returns {string} 格式化后的消息
 */
function formatCommitMessage(type, parsed) {
    return `${type}(${parsed.module}) : ${parsed.taskNumber} ${parsed.description}`;
}

/**
 * 显示类型选择菜单
 * @returns {Promise<string|undefined>} 选择的类型
 */
async function showTypeSelector() {
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    const defaultType = config.get('defaultType', 'feat');
    
    const items = COMMIT_TYPES.map(type => ({
        label: type.label,
        description: type.description,
        picked: type.label === defaultType
    }));
    
    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: '选择提交类型',
        matchOnDescription: true
    });
    
    return selected ? selected.label : undefined;
}

/**
 * 格式化当前选中的文本或当前行
 */
async function formatCommitMessageCommand() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('没有打开的编辑器');
        return;
    }
    
    const document = editor.document;
    const selection = editor.selection;
    
    // 获取要处理的文本
    let text;
    let range;
    
    if (selection.isEmpty) {
        // 如果没有选中文本，使用当前行
        const line = document.lineAt(selection.active.line);
        text = line.text;
        range = line.range;
    } else {
        // 使用选中的文本
        text = document.getText(selection);
        range = selection;
    }
    
    // 解析消息格式
    const parsed = parseCommitMessage(text);
    if (!parsed) {
        vscode.window.showWarningMessage('消息格式不匹配。期望格式：任务编号 模块名 : 描述');
        return;
    }
    
    // 显示类型选择菜单
    const selectedType = await showTypeSelector();
    if (!selectedType) {
        return; // 用户取消了选择
    }
    
    // 格式化消息
    const formattedMessage = formatCommitMessage(selectedType, parsed);
    
    // 替换文本
    await editor.edit(editBuilder => {
        editBuilder.replace(range, formattedMessage);
    });
    
    vscode.window.showInformationMessage('提交消息已格式化');
}

/**
 * 自动检测并提示格式化
 */
function setupAutoDetection() {
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    if (!config.get('autoDetect', true)) {
        return;
    }
    
    // 监听文本变化
    vscode.workspace.onDidChangeTextDocument(event => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.document !== editor.document) {
            return;
        }
        
        // 检查是否是Git提交消息文件
        const fileName = event.document.fileName;
        if (!fileName.includes('COMMIT_EDITMSG') && !fileName.includes('git-commit')) {
            return;
        }
        
        // 检查当前行是否符合格式
        const line = editor.document.lineAt(editor.selection.active.line);
        const parsed = parseCommitMessage(line.text);
        
        if (parsed) {
            // 显示格式化提示
            vscode.window.showInformationMessage(
                '检测到可格式化的提交消息',
                '格式化'
            ).then(selection => {
                if (selection === '格式化') {
                    formatCommitMessageCommand();
                }
            });
        }
    });
}

/**
 * 插件激活时调用
 * @param {vscode.ExtensionContext} context 
 */
function activate(context) {
    console.log('Git Commit Formatter 插件已激活');
    
    // 注册格式化命令
    const formatCommand = vscode.commands.registerCommand(
        'git-commit-formatter.format',
        formatCommitMessageCommand
    );
    
    // 注册自动格式化命令
    const autoFormatCommand = vscode.commands.registerCommand(
        'git-commit-formatter.autoFormat',
        () => {
            const config = vscode.workspace.getConfiguration('gitCommitFormatter');
            const currentValue = config.get('autoDetect', true);
            config.update('autoDetect', !currentValue, vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(
                `自动检测已${!currentValue ? '启用' : '禁用'}`
            );
        }
    );
    
    // 设置自动检测
    setupAutoDetection();
    
    // 添加到订阅列表
    context.subscriptions.push(formatCommand, autoFormatCommand);
}

/**
 * 插件停用时调用
 */
function deactivate() {
    console.log('Git Commit Formatter 插件已停用');
}

module.exports = {
    activate,
    deactivate
};
