const vscode = require('vscode');

// 用于跟踪是否正在处理提交
let isProcessingCommit = false;

/**
 * 提交类型配置
 */
const COMMIT_TYPES = [
    { label: 'feat', description: '新功能' },
    { label: 'fix', description: '修复bug' },
    { label: 'docs', description: '文档更新' },
    { label: 'style', description: '代码格式调整' },
    { label: 'refactor', description: '代码重构' },
    { label: 'perf', description: '性能优化' },
    { label: 'test', description: '测试相关' },
    { label: 'chore', description: '构建工具或辅助工具的变动' },
];

/**
 * 解析提交消息格式
 * 输入格式：13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
 * @param {string} message
 * @returns {object|null} 解析结果
 */
function parseCommitMessage(message) {
    // 匹配格式：数字 + 空格 + 模块名 + 冒号 + 描述
    // 更灵活的正则表达式，支持更多空格和格式变化
    const regex = /^(\d+)\s+([^:]+?)\s*[:：]\s*(.+)$/;
    const match = message.trim().match(regex);

    if (match) {
        return {
            taskNumber: match[1].trim(),
            module: match[2].trim(),
            description: match[3].trim(),
        };
    }

    // 尝试更宽松的格式匹配
    const looseRegex = /^(\d+)[\s\-_]+([^:：]+?)[\s]*[:：][\s]*(.+)$/;
    const looseMatch = message.trim().match(looseRegex);

    if (looseMatch) {
        return {
            taskNumber: looseMatch[1].trim(),
            module: looseMatch[2].trim(),
            description: looseMatch[3].trim(),
        };
    }

    return null;
}

/**
 * 格式化提交消息
 * @param {string} type 提交类型
 * @param {object} parsed 解析后的消息对象
 * @returns {string} 格式化后的消息
 */
function formatCommitMessage(type, parsed) {
    return `${type}(${parsed.module}) : ${parsed.taskNumber} ${parsed.description}`;
}

/**
 * 显示类型选择菜单
 * @returns {Promise<string|undefined>} 选择的类型
 */
async function showTypeSelector() {
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    const defaultType = config.get('defaultType', 'feat');

    const items = COMMIT_TYPES.map((type) => ({
        label: type.label,
        description: type.description,
        picked: type.label === defaultType,
    }));

    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: '选择提交类型',
        matchOnDescription: true,
    });

    return selected ? selected.label : undefined;
}

/**
 * 格式化当前选中的文本或当前行
 */
async function formatCommitMessageCommand() {
    try {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('没有打开的编辑器');
            return;
        }

        const document = editor.document;
        const selection = editor.selection;

        // 获取要处理的文本
        let text;
        let range;

        if (selection.isEmpty) {
            // 如果没有选中文本，使用当前行
            const line = document.lineAt(selection.active.line);
            text = line.text;
            range = line.range;
        } else {
            // 使用选中的文本
            text = document.getText(selection);
            range = selection;
        }

        console.log('处理的文本:', text);

        // 解析消息格式
        const parsed = parseCommitMessage(text);
        if (!parsed) {
            vscode.window.showWarningMessage(
                `消息格式不匹配。\n当前文本: "${text}"\n期望格式: "任务编号 模块名 : 描述"\n示例: "13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示"`
            );
            return;
        }

        console.log('解析结果:', parsed);

        // 显示类型选择菜单
        const selectedType = await showTypeSelector();
        if (!selectedType) {
            return; // 用户取消了选择
        }

        // 格式化消息
        const formattedMessage = formatCommitMessage(selectedType, parsed);
        console.log('格式化后的消息:', formattedMessage);

        // 替换文本
        await editor.edit((editBuilder) => {
            editBuilder.replace(range, formattedMessage);
        });

        vscode.window.showInformationMessage('提交消息已格式化');
    } catch (error) {
        console.error('格式化过程中出错:', error);
        vscode.window.showErrorMessage(`格式化失败: ${error.message}`);
    }
}

/**
 * 自动检测并提示格式化
 */
function setupAutoDetection() {
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    if (!config.get('autoDetect', true)) {
        return;
    }

    let lastCheckedText = '';
    let lastNotificationTime = 0;

    // 监听文本变化
    vscode.workspace.onDidChangeTextDocument((event) => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || event.document !== editor.document) {
            return;
        }

        // 检查是否是Git相关文件或者包含提交消息的文件
        const fileName = event.document.fileName.toLowerCase();
        const isGitFile =
            fileName.includes('commit_editmsg') ||
            fileName.includes('git-commit') ||
            fileName.includes('merge_msg') ||
            fileName.includes('tag_editmsg') ||
            event.document.languageId === 'git-commit' ||
            event.document.uri.scheme === 'git';

        // 如果不是Git文件，也检查当前行内容（适用于任何文本编辑器）
        if (!isGitFile) {
            // 只在用户明确要求时才在非Git文件中检测
            return;
        }

        // 获取当前行文本
        const currentLine = editor.selection.active.line;
        const line = editor.document.lineAt(currentLine);
        const currentText = line.text.trim();

        // 避免重复检测同样的文本
        if (currentText === lastCheckedText || currentText.length < 10) {
            return;
        }

        // 限制通知频率（避免过于频繁的提示）
        const now = Date.now();
        if (now - lastNotificationTime < 3000) {
            // 3秒内不重复提示
            return;
        }

        // 检查当前行是否符合格式
        const parsed = parseCommitMessage(currentText);

        if (parsed) {
            lastCheckedText = currentText;
            lastNotificationTime = now;

            // 显示格式化提示
            vscode.window.showInformationMessage('检测到可格式化的提交消息', '格式化', '忽略').then((selection) => {
                if (selection === '格式化') {
                    formatCommitMessageCommand();
                } else if (selection === '忽略') {
                    lastCheckedText = currentText; // 记住忽略的文本
                }
            });
        }
    });
}

/**
 * 自动格式化Git提交消息
 * @param {string} message 原始提交消息
 * @returns {Promise<string>} 格式化后的消息
 */
async function autoFormatGitCommit(message) {
    const parsed = parseCommitMessage(message);
    if (!parsed) {
        return message; // 如果不匹配格式，返回原消息
    }

    // 获取默认提交类型
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    const defaultType = config.get('defaultType', 'feat');

    // 询问用户是否要格式化
    const choice = await vscode.window.showInformationMessage(
        `检测到可格式化的提交消息，是否自动格式化？\n原消息: ${message}`,
        { modal: true },
        '使用默认类型(' + defaultType + ')',
        '选择类型',
        '跳过格式化'
    );

    if (choice === '跳过格式化') {
        return message;
    }

    let selectedType = defaultType;
    if (choice === '选择类型') {
        selectedType = await showTypeSelector();
        if (!selectedType) {
            return message; // 用户取消了选择
        }
    }

    const formattedMessage = formatCommitMessage(selectedType, parsed);
    console.log('自动格式化结果:', formattedMessage);
    return formattedMessage;
}

/**
 * 设置Git提交拦截
 * @param {vscode.ExtensionContext} context
 */
async function setupGitCommitInterception(context) {
    // 检查是否启用了提交拦截
    const config = vscode.workspace.getConfiguration('gitCommitFormatter');
    if (!config.get('interceptCommit', true)) {
        console.log('Git提交拦截已禁用');
        return;
    }

    try {
        // 获取Git扩展
        const gitExtension = vscode.extensions.getExtension('vscode.git');
        if (!gitExtension) {
            console.log('Git扩展未找到');
            return;
        }

        if (!gitExtension.isActive) {
            await gitExtension.activate();
        }

        const git = gitExtension.exports.getAPI(1);
        if (!git) {
            console.log('无法获取Git API');
            return;
        }

        console.log('Git API获取成功，设置提交拦截...');

        // 监听所有仓库
        git.repositories.forEach((repo) => {
            setupRepositoryInterception(repo);
        });

        // 监听新仓库
        git.onDidOpenRepository((repo) => {
            setupRepositoryInterception(repo);
        });

        // 监听配置变化
        vscode.workspace.onDidChangeConfiguration((e) => {
            if (e.affectsConfiguration('gitCommitFormatter.interceptCommit')) {
                const newValue = config.get('interceptCommit', true);
                if (newValue) {
                    vscode.window
                        .showInformationMessage('Git提交拦截已启用，重新加载窗口以生效', '重新加载')
                        .then((selection) => {
                            if (selection === '重新加载') {
                                vscode.commands.executeCommand('workbench.action.reloadWindow');
                            }
                        });
                } else {
                    vscode.window.showInformationMessage('Git提交拦截已禁用');
                }
            }
        });
    } catch (error) {
        console.error('设置Git提交拦截失败:', error);
    }
}

/**
 * 为单个仓库设置提交拦截
 * @param {any} repository Git仓库对象
 */
function setupRepositoryInterception(repository) {
    console.log('为仓库设置提交拦截:', repository.rootUri.path);

    // 监听提交消息变化
    repository.inputBox.onDidChange(async (message) => {
        if (isProcessingCommit || !message || message.length < 10) {
            return;
        }

        const parsed = parseCommitMessage(message);
        if (parsed) {
            console.log('检测到可格式化的提交消息:', message);
            // 这里我们不直接修改，而是在用户提交时拦截
        }
    });

    // 拦截提交操作
    const originalCommit = repository.commit;
    repository.commit = async function (message, opts) {
        console.log('拦截到提交操作，消息:', message);

        if (isProcessingCommit) {
            return originalCommit.call(this, message, opts);
        }

        isProcessingCommit = true;
        try {
            const formattedMessage = await autoFormatGitCommit(message);
            console.log('使用格式化后的消息提交:', formattedMessage);
            return originalCommit.call(this, formattedMessage, opts);
        } finally {
            isProcessingCommit = false;
        }
    };
}

/**
 * 插件激活时调用
 * @param {vscode.ExtensionContext} context
 */
function activate(context) {
    console.log('Git Commit Formatter 插件已激活');

    // 注册格式化命令
    const formatCommand = vscode.commands.registerCommand('git-commit-formatter.format', formatCommitMessageCommand);

    // 注册自动格式化开关命令
    const autoFormatCommand = vscode.commands.registerCommand('git-commit-formatter.autoFormat', () => {
        const config = vscode.workspace.getConfiguration('gitCommitFormatter');
        const currentValue = config.get('autoDetect', true);
        config.update('autoDetect', !currentValue, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage(`自动检测已${!currentValue ? '启用' : '禁用'}`);
    });

    // 注册测试命令
    const testCommand = vscode.commands.registerCommand('git-commit-formatter.test', () => {
        const testMessage = '13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示';
        const parsed = parseCommitMessage(testMessage);
        if (parsed) {
            vscode.window.showInformationMessage(
                `测试成功！解析结果: 任务号=${parsed.taskNumber}, 模块=${parsed.module}, 描述=${parsed.description}`
            );
        } else {
            vscode.window.showErrorMessage('测试失败：无法解析测试消息');
        }
    });

    // 注册切换提交拦截命令
    const toggleInterceptCommand = vscode.commands.registerCommand('git-commit-formatter.toggleIntercept', () => {
        const config = vscode.workspace.getConfiguration('gitCommitFormatter');
        const currentValue = config.get('interceptCommit', true);
        config.update('interceptCommit', !currentValue, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage(
            `Git提交拦截已${!currentValue ? '启用' : '禁用'}${!currentValue ? '，重新加载窗口以生效' : ''}`
        );
    });

    // 设置自动检测
    setupAutoDetection();

    // 设置Git提交拦截
    setupGitCommitInterception(context);

    // 添加到订阅列表
    context.subscriptions.push(formatCommand, autoFormatCommand, testCommand, toggleInterceptCommand);
}

/**
 * 插件停用时调用
 */
function deactivate() {
    console.log('Git Commit Formatter 插件已停用');
}

module.exports = {
    activate,
    deactivate,
};
