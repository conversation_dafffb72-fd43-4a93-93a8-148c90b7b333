# 更新日志

## [1.0.0] - 2024-09-26

### 新增功能
- 初始版本发布
- 支持自动识别任务编号格式的提交消息
- 提供8种提交类型选择（feat、fix、docs、style、refactor、perf、test、chore）
- 支持手动触发格式化功能
- 自动检测符合格式的提交消息并提示格式化
- 提供快捷键支持（Ctrl+Shift+G / Cmd+Shift+G）
- 支持右键菜单格式化
- 可配置的自动检测开关
- 可配置的默认提交类型

### 功能特点
- **消息格式转换**: 
  - 输入: `13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示`
  - 输出: `fix(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示`
- **智能检测**: 自动识别Git提交消息文件并提示格式化
- **类型选择**: 提供快速选择菜单选择提交类型
- **配置灵活**: 支持自定义默认类型和自动检测开关

### 技术实现
- 使用正则表达式解析消息格式
- 集成VSCode命令系统
- 支持文本编辑器事件监听
- 提供配置选项管理
