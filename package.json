{"name": "git-commit-formatter", "displayName": "Git Commit Formatter", "description": "VSCode插件，用于格式化Git提交消息，自动添加类型前缀和任务编号", "version": "1.0.0", "publisher": "your-publisher-name", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "keywords": ["git", "commit", "formatter", "message"], "activationEvents": ["onCommand:git-commit-formatter.format", "onCommand:git-commit-formatter.autoFormat"], "main": "./extension.js", "contributes": {"commands": [{"command": "git-commit-formatter.format", "title": "格式化Git提交消息", "category": "Git Commit Formatter"}, {"command": "git-commit-formatter.autoFormat", "title": "自动格式化Git提交消息", "category": "Git Commit Formatter"}], "keybindings": [{"command": "git-commit-formatter.format", "key": "ctrl+shift+g", "mac": "cmd+shift+g", "when": "editorTextFocus"}], "menus": {"editor/context": [{"command": "git-commit-formatter.format", "when": "editorTextFocus", "group": "1_modification"}]}, "configuration": {"title": "Git Commit Formatter", "properties": {"gitCommitFormatter.autoDetect": {"type": "boolean", "default": true, "description": "自动检测并提示格式化符合格式的提交消息"}, "gitCommitFormatter.defaultType": {"type": "string", "default": "feat", "enum": ["feat", "fix", "docs", "style", "refactor", "perf", "test", "chore"], "description": "默认的提交类型"}}}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@types/vscode": "^1.60.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/git-commit-formatter.git"}, "license": "MIT"}