# Git Commit Formatter 演示指南

## 🚀 快速开始

### 1. 安装插件

#### 方法一：使用安装脚本
```bash
# Windows
install.bat

# Linux/Mac
./install.sh
```

#### 方法二：手动安装
```bash
npm install
npm install -g vsce
vsce package
```
然后在VSCode中安装生成的 `.vsix` 文件。

### 2. 验证安装

1. 打开VSCode
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 输入 "测试消息解析"
4. 如果看到成功消息，说明插件安装正确

## 📝 使用演示

### 演示1：基本格式化

1. **打开测试文件**：
   ```
   code test-commit-message.txt
   ```

2. **选择要格式化的消息**：
   ```
   13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示
   ```

3. **执行格式化**：
   - 选中上面的文本
   - 按 `Ctrl+Shift+G`

4. **选择提交类型**：
   - 在弹出菜单中选择 `fix`

5. **查看结果**：
   ```
   fix(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示
   ```

### 演示2：不同提交类型

尝试不同的提交类型：

**原始消息**：
```
12345 用户管理模块 : 添加用户权限验证功能
```

**选择不同类型的结果**：
- `feat` → `feat(用户管理模块) : 12345 添加用户权限验证功能`
- `fix` → `fix(用户管理模块) : 12345 添加用户权限验证功能`
- `docs` → `docs(用户管理模块) : 12345 添加用户权限验证功能`

### 演示3：支持的格式变化

插件支持以下格式变化：

```
# 标准格式
13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示

# 中文冒号
33333 数据库层 ： 修复连接池问题

# 额外空格
44444  前端界面-用户体验  :  改进响应式布局

# 复杂模块名
55555 订单处理系统-核心业务逻辑 : 实现订单状态机制
```

## 🎯 实际使用场景

### 场景1：在普通文本文件中

1. 创建或打开任意文本文件
2. 输入符合格式的提交消息
3. 选中消息文本
4. 使用快捷键或命令格式化

### 场景2：在Git提交消息中

1. 使用 `git commit` 命令
2. 在编辑器中输入符合格式的消息
3. 插件会自动检测并提示格式化（如果启用了自动检测）

### 场景3：批量处理

1. 准备多条提交消息
2. 逐一选中并格式化
3. 或者复制到文本文件中批量处理

## ⚙️ 配置选项

### 自动检测设置

```json
{
  "gitCommitFormatter.autoDetect": true,  // 启用自动检测
  "gitCommitFormatter.defaultType": "feat"  // 默认提交类型
}
```

### 切换自动检测

- 命令面板 → "切换自动检测"
- 或者在设置中手动修改

## 🔧 快捷键和命令

| 功能 | 快捷键 | 命令 |
|------|--------|------|
| 格式化消息 | `Ctrl+Shift+G` | `git-commit-formatter.format` |
| 切换自动检测 | - | `git-commit-formatter.autoFormat` |
| 测试解析 | - | `git-commit-formatter.test` |

## 📊 支持的提交类型

| 类型 | 说明 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat(用户模块) : 12345 添加登录功能` |
| `fix` | 修复bug | `fix(订单系统) : 67890 修复支付异常` |
| `docs` | 文档更新 | `docs(API文档) : 11111 更新接口说明` |
| `style` | 代码格式 | `style(前端) : 22222 统一代码风格` |
| `refactor` | 代码重构 | `refactor(核心逻辑) : 33333 优化算法` |
| `perf` | 性能优化 | `perf(数据库) : 44444 优化查询性能` |
| `test` | 测试相关 | `test(单元测试) : 55555 添加测试用例` |
| `chore` | 构建工具 | `chore(构建) : 66666 更新依赖包` |

## 🐛 常见问题

### Q: 快捷键不工作？
A: 确保文本编辑器有焦点，或尝试通过命令面板使用。

### Q: 消息格式不匹配？
A: 检查格式：`数字 模块名 : 描述`，使用测试命令验证。

### Q: 自动检测不工作？
A: 检查设置中的自动检测开关，当前主要支持Git提交文件。

## 🎉 完成！

现在您已经掌握了Git Commit Formatter的使用方法。开始享受规范化的提交消息格式吧！
