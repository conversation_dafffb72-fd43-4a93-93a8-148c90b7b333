# 测试提交消息格式化

以下是一些测试用的提交消息，可以用来测试插件功能：

## 正确格式的消息（应该能被识别和格式化）

13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示

12345 用户管理模块 : 添加用户权限验证功能

67890 订单处理系统 : 修复订单状态更新bug

11111 报表生成器 : 优化查询性能

22222 API接口层 : 更新接口文档

33333 数据库层 ： 修复连接池问题

44444 前端界面-用户体验 : 改进响应式布局

## 错误格式的消息（不应该被识别）

这是一个普通的提交消息
    
修复了一个bug

添加新功能

主数据模型-主数据维护 : 缺少任务编号

13386 : 缺少模块名称

13386 主数据模型-主数据维护 缺少冒号分隔符

## 使用说明

1. 选中上面任一正确格式的消息
2. 按 Ctrl+Shift+G (或 Cmd+Shift+G)
3. 选择提交类型
4. 查看格式化结果
