# Git Commit Formatter

VSCode插件，用于格式化Git提交消息，自动添加类型前缀和任务编号。

## 功能特点

- 自动识别任务编号格式（如 "13386 主数据模型-主数据维护 :  主数据查询-多查询条件提升展示"）
- 重新格式化为 "(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示"
- 提供提交类型选择（feat、fix、docs等）
- 支持手动触发格式化
- 自动检测符合格式的提交消息并提示格式化

## 使用方法

当输入符合格式的消息（如 "13386 : 主数据查询-多查询条件提升展示"）时，插件会自动提示格式化。

## 提交类型说明

- **feat**: 新功能
- **fix**: 修复bug  
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建工具或辅助工具的变动

## 示例

输入：`13386 主数据模型-主数据维护 : 主数据查询-多查询条件提升展示`

选择类型：`fix`

输出：`fix(主数据模型-主数据维护) : 13386 主数据查询-多查询条件提升展示`
