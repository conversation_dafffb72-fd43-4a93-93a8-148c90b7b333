@echo off
chcp 65001 >nul
echo === Git Commit Formatter VSCode Extension 安装脚本 ===
echo.

REM 检查是否安装了 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    echo    下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

REM 检查是否安装了 npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo ✅ npm 版本:
npm --version

REM 安装依赖
echo.
echo 📦 安装依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功

REM 检查是否安装了 vsce
vsce --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo 📦 安装 vsce (VSCode Extension Manager)...
    npm install -g vsce
    
    if %errorlevel% neq 0 (
        echo ❌ vsce 安装失败
        pause
        exit /b 1
    )
    
    echo ✅ vsce 安装成功
) else (
    echo ✅ vsce 已安装:
    vsce --version
)

REM 打包插件
echo.
echo 📦 打包插件...
vsce package

if %errorlevel% neq 0 (
    echo ❌ 插件打包失败
    pause
    exit /b 1
)

echo ✅ 插件打包成功

REM 显示安装说明
echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作：
echo 1. 在 VSCode 中按 Ctrl+Shift+P 打开命令面板
echo 2. 输入 'Extensions: Install from VSIX...'
echo 3. 选择生成的 .vsix 文件进行安装
echo.
echo 或者：
echo 1. 按 F5 在开发模式下运行插件
echo 2. 在新打开的 VSCode 窗口中测试插件功能
echo.
echo 📖 使用说明请查看 USAGE.md 文件
echo.

REM 列出生成的文件
echo 📁 生成的文件:
dir *.vsix 2>nul || echo    (未找到 .vsix 文件，请检查打包过程)

echo.
echo ✨ 祝您使用愉快！
pause
